<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
        bordered
        content-style="padding: 24px;"
        :collapsed-width="0"
        :width="280"
        show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br/>
      <!-- 搜索框 -->
      <div style="margin-bottom: 16px;">
        <NInput
            v-model:value="searchKeyword"
            placeholder="搜索项目或模块..."
            clearable
            @input="handleSearch"
            @clear="handleClearSearch"
        >
          <template #prefix>
            <TheIcon icon="material-symbols:search" :size="16"/>
          </template>
        </NInput>
      </div>

      <!-- 项目模块树 -->
      <NTree
          block-line
          :data="filteredTreeData"
          key-field="key"
          label-field="label"
          :expanded-keys="expandedKeys"
          :node-props="treeNodeProps"
          :selected-keys="selectedKeys"
          @update:expanded-keys="handleExpandedKeysChange"
      >
        <template #prefix="{ option }">
          <TheIcon
              :icon="option.type === 'project' ? 'material-symbols:folder' : 'material-symbols:topic'"
              :size="16"
              :style="{ color: option.type === 'project' ? '#1890ff' : '#52c41a' }"
          />
        </template>
        <template #default="{ option }">
          <span v-html="highlightSearchKeyword(option.label)"></span>
        </template>
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="selectedProjectName ? `${selectedProjectName} - 功能测试用例` : '功能测试用例'">
        <template #action>
          <div style="display: flex; gap: 12px; align-items: center;">
            <!-- 导入测试用例按钮 -->
            <NButton
                v-if="selectedProjectId"
                type="info"
                size="small"
                @click="handleImport"
            >
              <TheIcon icon="material-symbols:upload" :size="18" class="mr-5"/>
              导入
            </NButton>

            <!-- 重置编号按钮 -->
<!--            <NPopconfirm-->
<!--                v-if="selectedProjectId"-->
<!--                @positive-click="handleResetNumbers"-->
<!--            >-->
<!--              <template #trigger>-->
<!--                <NButton type="warning" size="small">-->
<!--                  <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5"/>-->
<!--                  重置编号-->
<!--                </NButton>-->
<!--              </template>-->
<!--              确认重置该项目下所有测试用例的编号吗？-->
<!--            </NPopconfirm>-->

            <!-- 新建测试用例按钮 -->
            <NButton
                v-if="selectedProjectId"
                type="primary"
                size="small"
                @click="handleAdd"
            >
              <TheIcon icon="material-symbols:add" :size="18" class="mr-5"/>
              新建
            </NButton>
          </div>
        </template>
        <!-- 表格 -->
        <CrudTable
            ref="$table"
            v-model:query-items="queryItems"
            :columns="columns"
            :get-data="testCaseApi.getTestCases"
            @reset="handleReset"
            @on-checked="handleTableChecked"
        >
          <template #queryBar>
            <QueryBarItem label="" :label-width="80">
              <NInput
                  v-model:value="queryItems.case_name"
                  clearable
                  type="text"
                  placeholder="请输入用例名称"
                  @keypress.enter="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                  v-model:value="queryItems.case_level"
                  clearable
                  :options="levelOptions"
                  placeholder="等级"
                  style="width: 100px"
                  @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <!--            <QueryBarItem label="" :label-width="80">-->
            <!--              <NSelect-->
            <!--                v-model:value="queryItems.module_id"-->
            <!--                clearable-->
            <!--                :options="moduleOptions"-->
            <!--                placeholder="模块"-->
            <!--                style="width: 100px"-->
            <!--                @update:value="$table?.handleSearch()"-->
            <!--              />-->
            <!--            </QueryBarItem>-->
            <QueryBarItem label="" :label-width="50">
              <NSelect
                  v-model:value="queryItems.status"
                  clearable
                  :options="statusOptions"
                  placeholder="状态"
                  style="width: 100px"
                  @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="50">
              <NSelect
                  v-model:value="queryItems.source"
                  clearable
                  :options="sourceOptions"
                  placeholder="来源"
                  style="width: 100px"
                  @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                  v-model:value="queryItems.is_smoke"
                  clearable
                  :options="[
                  { label: '是', value: true },
                  { label: '否', value: false }
                ]"
                  placeholder="冒烟"
                  style="width: 100px"
                  @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
          </template>
          <template #actions>
            <!-- 批量操作按钮 -->
            <NButton
                type="error"
                :disabled="selectedRowKeys.length === 0"
                @click="handleBatchDelete"
                :style="{ marginRight: '20px' }"
            >
              删除
            </NButton>

            <NButton
                type="warning"
                :disabled="selectedRowKeys.length === 0"
                @click="handleBatchUpdateStatus"
            >
              状态
            </NButton>
          </template>
        </CrudTable>
      </CommonPage>
    </NLayoutContent>
  </NLayout>

  <!-- 新增/编辑弹窗 -->
  <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      :show-footer="true"
      @save="handleSave"
      @close="handleModalClose"
      width="900px"
  >
    <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="modalRules"
    >
      <NFormItem label="用例名称" path="case_name">
        <NInput v-model:value="modalForm.case_name" clearable placeholder="请输入用例名称"/>
      </NFormItem>

      <!-- 第一行：用例等级、所属项目、所属模块 -->
      <div style="display: flex; gap: 16px;">
        <NFormItem label="用例等级" path="case_level" style="flex: 1;">
          <NSelect
              v-model:value="modalForm.case_level"
              :options="levelOptions"
              placeholder="请选择用例等级"
          />
        </NFormItem>
        <NFormItem label="所属项目" path="project_id" style="flex: 1;">
          <NSelect
              v-model:value="modalForm.project_id"
              :options="projectOption.map(p => ({ label: p.name, value: String(p.id) }))"
              placeholder="请选择所属项目"
              @update:value="handleProjectChange"
          />
        </NFormItem>
        <NFormItem label="所属模块" path="module_id" style="flex: 1;">
          <NSelect
              v-model:value="modalForm.module_id"
              :options="moduleOptions"
              placeholder="请选择所属模块"
              clearable
          />
        </NFormItem>
      </div>

      <NFormItem label="前置条件" path="precondition">
        <NInput
            v-model:value="modalForm.precondition"
            type="textarea"
            :rows="3"
            placeholder="请输入前置条件（可选）"
        />
      </NFormItem>
      <NFormItem label="用例步骤" path="test_steps">
        <NInput
            v-model:value="modalForm.test_steps"
            type="textarea"
            :rows="5"
            placeholder="请输入用例步骤"
        />
      </NFormItem>
      <NFormItem label="预期结果" path="expected_result">
        <NInput
            v-model:value="modalForm.expected_result"
            type="textarea"
            :rows="3"
            placeholder="请输入预期结果"
        />
      </NFormItem>

      <!-- 第二行：是否冒烟用例、状态、来源 -->
      <div style="display: flex; gap: 16px;">
        <NFormItem label="是否冒烟用例" path="is_smoke" style="flex: 1;">
          <NSwitch v-model:value="modalForm.is_smoke"/>
        </NFormItem>
        <NFormItem label="状态" path="status" style="flex: 1;">
          <NSelect
              v-model:value="modalForm.status"
              :options="statusOptions"
              placeholder="请选择状态"
          />
        </NFormItem>
        <NFormItem label="来源" path="source" style="flex: 1;">
          <NSelect
              v-model:value="modalForm.source"
              :options="sourceOptions"
              placeholder="请选择来源"
          />
        </NFormItem>
      </div>
    </NForm>
  </CrudModal>

  <!-- 复制用例弹窗 -->
  <CrudModal
      v-model:visible="copyModalVisible"
      title="复制测试用例"
      @save="executeCopy"
  >
    <NForm
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="copyForm"
    >
      <NFormItem label="新用例名称" path="case_name">
        <NInput v-model:value="copyForm.case_name" clearable placeholder="请输入新用例名称"/>
      </NFormItem>
      <NFormItem label="目标项目" path="project_id">
        <NSelect
            v-model:value="copyForm.project_id"
            :options="projectOption.map(p => ({ label: p.name, value: p.id }))"
            placeholder="请选择目标项目"
        />
      </NFormItem>
    </NForm>
  </CrudModal>

  <!-- 批量修改状态弹窗 -->
  <CrudModal
      v-model:visible="batchStatusModalVisible"
      title="批量修改状态"
      @save="executeBatchUpdateStatus"
  >
    <NForm
        label-placement="left"
        label-align="left"
        :label-width="100"
    >
      <NFormItem label="选择状态">
        <NSelect
            v-model:value="batchStatus"
            :options="statusOptions"
            placeholder="请选择状态"
        />
      </NFormItem>
      <NFormItem>
        <NText depth="3">
          将为选中的测试用例修改状态
        </NText>
      </NFormItem>
    </NForm>
  </CrudModal>

  <!-- 导入测试用例弹窗 -->
  <NModal
      v-model:show="importModalVisible"
      preset="card"
      title="导入功能测试用例"
      style="width: 600px;"
      :mask-closable="false"
      :closable="true"
  >
    <div>
      <NAlert type="info" style="margin-bottom: 16px;">
        <template #header>导入说明</template>
        <div>
          <p>1. 请先下载模板文件，按照模板格式填写测试用例数据</p>
          <p>2. 用例等级只能填写：高、中、低</p>
          <p>3. 是否冒烟用例只能填写：是、否</p>
          <p>4. 导入的用例状态默认为"待审核"，来源为"人工"</p>
        </div>
      </NAlert>

      <div style="margin-bottom: 16px;">
        <NButton type="primary" @click="handleDownloadTemplate" :loading="downloadLoading">
          <TheIcon icon="material-symbols:download" :size="18" class="mr-5"/>
          下载模板
        </NButton>
      </div>

      <!-- 模块选择 -->
      <div style="margin-bottom: 16px;">
        <NFormItem label="默认模块" label-placement="left" :label-width="80">
          <NSelect
              v-model:value="selectedModuleId"
              :options="moduleOptions"
              placeholder="选择默认模块（可选）"
              clearable
              style="width: 100%"
          />
        </NFormItem>
        <NText depth="3" style="font-size: 12px; margin-top: 4px;">
          选择后，所有导入的用例都将分配到此模块。如果不选择，将根据Excel中的"所属模块"列进行匹配。
        </NText>
      </div>

      <NUpload
          ref="uploadRef"
          :file-list="fileList"
          :max="1"
          accept=".xlsx,.xls"
          @change="handleFileChange"
          @remove="handleFileRemove"
      >
        <NUploadDragger>
          <div style="margin-bottom: 12px;">
            <TheIcon icon="material-symbols:upload-file" :size="48" style="color: #18a058;"/>
          </div>
          <NText style="font-size: 16px">点击或者拖动文件到该区域来上传</NText>
          <NP depth="3" style="margin: 8px 0 0 0">
            支持 .xlsx 和 .xls 格式的Excel文件
          </NP>
        </NUploadDragger>
      </NUpload>

      <!-- 导入结果显示 -->
      <div v-if="importResult" style="margin-top: 16px;">
        <NAlert
            :type="importResult.success ? 'success' : 'error'"
            :title="importResult.message"
        >
          <div v-if="importResult.data">
            <p v-if="importResult.data.success_count > 0">
              成功导入：{{ importResult.data.success_count }} 条
            </p>
            <p v-if="importResult.data.error_count > 0">
              失败：{{ importResult.data.error_count }} 条
            </p>
            <div v-if="importResult.data.error_messages && importResult.data.error_messages.length > 0">
              <p style="margin-top: 8px; font-weight: bold;">错误详情：</p>
              <ul style="margin: 4px 0; padding-left: 20px;">
                <li v-for="(error, index) in importResult.data.error_messages" :key="index" style="margin: 2px 0;">
                  {{ error }}
                </li>
              </ul>
            </div>
          </div>
        </NAlert>
      </div>
    </div>

    <template #footer>
      <div style="text-align: right;">
        <NButton @click="handleCloseImportModal" style="margin-right: 12px;">取消</NButton>
        <NButton
            type="primary"
            @click="handleExecuteImport"
            :loading="importLoading"
            :disabled="fileList.length === 0"
        >
          开始导入
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup>
import {h, onMounted, ref, computed, nextTick, watch} from 'vue'
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NTree,
  NButton,
  NInput,
  NSelect,
  NSwitch,
  NTag,
  NPopconfirm,
  NForm,
  NFormItem,
  NModal,
  NAlert,
  NUpload,
  NUploadDragger,
  NText,
  NP,
  NPopover,
  NSpace,
  useMessage,
  useDialog
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import {formatDate, renderIcon} from '@/utils'
import {useCRUD} from '@/composables'
import testCaseApi from '@/api/testCase.js'
import projectApi from '@/api/project'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({name: '功能测试用例'})

const $message = useMessage()
const $dialog = useDialog()
const $table = ref(null)

// 项目相关
const projectOption = ref([])
const selectedKeys = ref([])
const selectedProjectId = ref(null)
const selectedProjectName = ref('')
const moduleOptions = ref([])

// 搜索相关
const searchKeyword = ref('')
const expandedKeys = ref([])
const treeData = ref([])
const filteredTreeData = ref([])

// 批量操作相关
const selectedRowKeys = ref([])
const batchStatusModalVisible = ref(false)
const batchStatus = ref('pending')

// 树节点属性
const treeNodeProps = ({option}) => {
  return {
    onClick() {
      if (option.type === 'project') {
        // 点击项目节点 - 显示项目下所有测试用例
        selectedKeys.value = [option.key]
        selectedProjectId.value = option.projectId
        selectedProjectName.value = option.label
        queryItems.value.project_id = option.projectId
        queryItems.value.module_id = null // 清空模块筛选，显示项目下所有用例
        queryItems.value.module_ids = null // 清空多模块筛选

        // 展开当前选中项目的所有模块
        expandAllProjectModules(option.projectId)

        // 加载该项目的模块
        loadModuleOptions(option.projectId)
        // 刷新表格
        $table.value?.handleSearch()
      } else if (option.type === 'module') {
        // 点击模块节点 - 显示该模块及其所有子模块的测试用例
        selectedKeys.value = [option.key]
        selectedProjectId.value = option.projectId
        selectedProjectName.value = option.projectName
        queryItems.value.project_id = option.projectId

        // 获取该模块及其所有子模块的ID
        const moduleIds = getAllModuleIdsFromTree(option.moduleId, option)
        if (moduleIds.length === 1) {
          // 只有一个模块，使用单模块查询
          queryItems.value.module_id = option.moduleId
          queryItems.value.module_ids = null
        } else {
          // 有多个模块（包含子模块），使用多模块查询
          queryItems.value.module_id = null
          queryItems.value.module_ids = moduleIds.join(',')
        }

        // 加载该项目的模块
        loadModuleOptions(option.projectId)
        // 刷新表格
        $table.value?.handleSearch()
      }
    }
  }
}

// 选项数据
const levelOptions = [
  {label: '低', value: 'low'},
  {label: '中', value: 'medium'},
  {label: '高', value: 'high'}
]

const statusOptions = [
  {label: '待审核', value: 'pending'},
  {label: '已审核', value: 'approved'}
]

const sourceOptions = [
  {label: '人工', value: 'manual'},
  {label: 'AI', value: 'ai'}
]

// 复制相关状态
const copyModalVisible = ref(false)
const copyForm = ref({
  id: null,
  case_name: '',
  project_id: null
})

// 导入相关状态
const importModalVisible = ref(false)
const importLoading = ref(false)
const downloadLoading = ref(false)
const fileList = ref([])
const uploadRef = ref(null)
const importResult = ref(null)
const selectedModuleId = ref(null)

// CRUD配置
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd: originalHandleAdd,
  handleDelete,
  handleEdit: originalHandleEdit,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleModalClose
} = useCRUD({
  name: '功能测试用例',
  initForm: {
    case_name: '',
    case_level: 'medium',
    precondition: '',
    test_steps: '',
    expected_result: '',
    is_smoke: false,
    status: 'pending',
    source: 'manual',
    project_id: '',
    module_id: null,
  },
  doCreate: testCaseApi.createTestCase,
  doUpdate: testCaseApi.updateTestCase,
  doDelete: testCaseApi.deleteTestCase,
  refresh: () => $table.value?.handleSearch()
})

// 表单验证规则
const modalRules = {
  case_name: [{required: true, message: '请输入用例名称', trigger: 'blur'}],
  case_level: [{required: true, message: '请选择用例等级', trigger: 'change'}],
  project_id: [{required: true, message: '请选择所属项目', trigger: 'change'}],
  test_steps: [{required: true, message: '请输入用例步骤', trigger: 'blur'}],
  expected_result: [{required: true, message: '请输入预期结果', trigger: 'blur'}],
  status: [{required: true, message: '请选择状态', trigger: 'change'}],
  source: [{required: true, message: '请选择来源', trigger: 'change'}]
}

// 查询参数
const queryItems = ref({
  case_name: null,
  case_level: null,
  module_id: null,
  module_ids: null,
  status: null,
  source: null,
  is_smoke: null,
  project_id: null
})

// 表格列配置
const columns = [
  {
    type: 'selection',
    width: 50
  },
  {
    title: '用例编号',
    key: 'case_number',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '用例等级',
    key: 'case_level',
    width: 100,
    render(row) {
      const levelMap = {
        low: {text: '低', type: 'info'},
        medium: {text: '中', type: 'warning'},
        high: {text: '高', type: 'error'}
      }
      const level = levelMap[row.case_level] || {text: row.case_level, type: 'default'}
      return h(NTag, {type: level.type, size: 'small'}, {default: () => level.text})
    }
  },
  {
    title: '前置条件',
    key: 'precondition',
    width: 150,
    ellipsis: {
      tooltip: true
    },
    render(row) {
      return row.precondition || '-'
    }
  },
  {
    title: '用例步骤',
    key: 'test_steps',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '预期结果',
    key: 'expected_result',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '冒烟用例',
    key: 'is_smoke',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_smoke ? 'success' : 'default',
        size: 'small'
      }, {
        default: () => row.is_smoke ? '是' : '否'
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        pending: {text: '待审核', type: 'warning'},
        approved: {text: '已审核', type: 'success'}
      }
      const status = statusMap[row.status] || {text: row.status, type: 'default'}

      return h(NPopover, {
        trigger: 'click',
        placement: 'bottom'
      }, {
        trigger: () => h(NTag, {
          type: status.type,
          size: 'small',
          style: 'cursor: pointer;'
        }, {default: () => status.text}),
        default: () => h('div', {
          style: 'padding: 8px;'
        }, [
          h('div', {
            style: 'margin-bottom: 8px; font-weight: 500; color: #666;'
          }, '选择状态:'),
          h(NSpace, {
            vertical: true,
            size: 'small'
          }, {
            default: () => Object.entries(statusMap).map(([value, config]) =>
                h(NTag, {
                  type: config.type,
                  size: 'small',
                  style: 'cursor: pointer; width: 80px; text-align: center;',
                  bordered: row.status === value,
                  onClick: () => handleUpdateStatus(row.id, value)
                }, {
                  default: () => config.text
                })
            )
          })
        ])
      })
    }
  },
  {
    title: '来源',
    key: 'source',
    width: 80,
    render(row) {
      const sourceMap = {
        manual: {text: '人工', type: 'info'},
        ai: {text: 'AI', type: 'success'}
      }
      const source = sourceMap[row.source] || {text: row.source, type: 'default'}
      return h(NTag, {type: source.type, size: 'small'}, {default: () => source.text})
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return formatDate(row.created_at)
    }
  },
  {
    title: '创建用户',
    key: 'username',
    width: 120,
    render(row) {
      return row.username || '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return h('div', {style: 'display: flex; gap: 8px; justify-content: center;'}, [
        h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              secondary: true,
              onClick: () => handleEdit(row)
            },
            {default: () => '编辑'}
        ),
        h(
            NButton,
            {
              size: 'small',
              type: 'info',
              secondary: true,
              onClick: () => handleCopy(row)
            },
            {default: () => '复制'}
        ),
        // h(
        //   NPopconfirm,
        //   {
        //     onPositiveClick: () => handleDelete({ id: row.id })
        //   },
        //   {
        //     default: () => '确认删除',
        //     trigger: () => h(
        //       NButton,
        //       {
        //         size: 'small',
        //         type: 'error'
        //       },
        //       { default: () => '删除' }
        //     )
        //   }
        // )
      ])
    }
  }
]

// API实例
const api = testCaseApi

// 处理项目选择
const handleProjectSelect = async (project) => {
  try {
    selectedProjectId.value = project.id
    selectedProjectName.value = project.name
    selectedKeys.value = [`project_${project.id}`]

    // 加载模块选项
    await loadModuleOptions(project.id)

    // 更新查询条件并刷新表格
    queryItems.value.project_id = project.id
    queryItems.value.module_id = null
    $table.value?.handleSearch()
  } catch (error) {
    console.error('选择项目失败:', error)
  }
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []

    // 构建项目和模块的树形数据
    await buildTreeData()

    // 默认选择第一个项目
    if (projectOption.value.length > 0 && !selectedProjectId.value) {
      const firstProject = projectOption.value[0]
      selectedProjectId.value = firstProject.id
      selectedProjectName.value = firstProject.name
      selectedKeys.value = [`project_${firstProject.id}`]
      queryItems.value.project_id = firstProject.id
      await loadModuleOptions(firstProject.id)
      // 等待树形数据构建完成后再展开第一个项目
      await nextTick()
      expandAllProjectModules(firstProject.id)
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 构建项目和模块的树形数据
const buildTreeData = async () => {
  try {
    const tree = []

    for (const project of projectOption.value) {
      // 获取项目的模块树
      const moduleResponse = await projectApi.getProjectModuleTree({project_id: project.id})
      const modules = moduleResponse.data || []

      // 递归构建模块节点
      const buildModuleNodes = (moduleList, projectId, projectName) => {
        return moduleList.map(module => {
          const moduleNode = {
            key: `module_${module.id}`,
            label: module.name,
            type: 'module',
            projectId: projectId,
            projectName: projectName,
            moduleId: module.id
          }

          // 只有当模块有子模块时才添加children属性
          if (module.children && module.children.length > 0) {
            moduleNode.children = buildModuleNodes(module.children, projectId, projectName)
          }

          return moduleNode
        })
      }

      // 构建项目节点
      const projectNode = {
        key: `project_${project.id}`,
        label: project.name,
        type: 'project',
        projectId: project.id
      }

      // 只有当项目有模块时才添加children属性
      if (modules && modules.length > 0) {
        projectNode.children = buildModuleNodes(modules, project.id, project.name)
      }

      tree.push(projectNode)
    }

    treeData.value = tree
    filteredTreeData.value = tree

    // 默认展开当前选中的项目及其所有模块
    if (selectedProjectId.value) {
      expandAllProjectModules(selectedProjectId.value)
    } else {
      expandedKeys.value = []
    }
  } catch (error) {
    console.error('构建树形数据失败:', error)
  }
}

// 加载模块选项
const loadModuleOptions = async (projectId) => {
  try {
    const response = await projectApi.getProjectModuleTree({project_id: projectId})
    const modules = response.data || []

    // 扁平化模块树，保持层级关系，只有叶子节点可选
    const flattenModules = (modules, result = [], level = 0) => {
      modules.forEach(module => {
        const prefix = '　'.repeat(level) // 使用全角空格缩进
        const hasChildren = module.children && module.children.length > 0

        result.push({
          label: `${prefix}${module.name}`,
          value: module.id,
          disabled: hasChildren // 有子模块的节点禁用，只能选择叶子节点
        })

        if (hasChildren) {
          flattenModules(module.children, result, level + 1)
        }
      })
      return result
    }

    moduleOptions.value = flattenModules(modules)
  } catch (error) {
    console.error('加载模块选项失败:', error)
    moduleOptions.value = []
  }
}

// 搜索相关函数
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    filteredTreeData.value = treeData.value
    // 清空搜索时，展开当前选中项目的所有模块
    if (selectedProjectId.value) {
      expandAllProjectModules(selectedProjectId.value)
    } else {
      expandedKeys.value = []
    }
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const filtered = []
  const newExpandedKeys = []

  // 递归过滤函数 - 改进版，确保所有匹配的节点都能显示
  const filterNode = (node) => {
    const nodeMatches = node.label.toLowerCase().includes(keyword)
    let hasMatchingChildren = false
    let filteredChildren = []

    if (node.children && node.children.length > 0) {
      filteredChildren = node.children.map(child => filterNode(child)).filter(Boolean)
      hasMatchingChildren = filteredChildren.length > 0
    }

    // 如果当前节点匹配或有匹配的子节点，则包含此节点
    if (nodeMatches || hasMatchingChildren) {
      const filteredNode = {
        ...node
      }

      // 只有当有过滤后的子节点时才添加children属性
      if (filteredChildren.length > 0) {
        filteredNode.children = filteredChildren
        // 展开包含匹配项的节点
        newExpandedKeys.push(node.key)
      }
      // 如果节点本身匹配但没有子节点，不添加children属性

      return filteredNode
    }

    return null
  }

  // 递归收集所有需要展开的节点
  const collectExpandedKeys = (node, keyword) => {
    const nodeMatches = node.label.toLowerCase().includes(keyword)

    if (nodeMatches) {
      newExpandedKeys.push(node.key)
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        collectExpandedKeys(child, keyword)
      })
    }
  }

  treeData.value.forEach(node => {
    const filteredNode = filterNode(node)
    if (filteredNode) {
      filtered.push(filteredNode)
      // 收集所有需要展开的节点
      collectExpandedKeys(node, keyword)
    }
  })

  filteredTreeData.value = filtered
  expandedKeys.value = [...new Set(newExpandedKeys)] // 去重
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  filteredTreeData.value = treeData.value
  // 清空搜索时，展开当前选中项目的所有模块
  if (selectedProjectId.value) {
    expandAllProjectModules(selectedProjectId.value)
  } else {
    expandedKeys.value = []
  }
}

const handleExpandedKeysChange = (keys) => {
  expandedKeys.value = keys
}

// 展开指定项目的所有模块
const expandAllProjectModules = (projectId) => {
  const keysToExpand = [`project_${projectId}`]

  // 递归收集所有模块节点的key
  const collectModuleKeys = (nodes) => {
    nodes.forEach(node => {
      if (node.type === 'module') {
        keysToExpand.push(node.key)
      }
      if (node.children && node.children.length > 0) {
        collectModuleKeys(node.children)
      }
    })
  }

  // 找到对应的项目节点并收集其所有模块key
  const projectNode = treeData.value.find(node => node.projectId === projectId)
  if (projectNode && projectNode.children) {
    collectModuleKeys(projectNode.children)
  }

  expandedKeys.value = keysToExpand
}

// 从树形数据中获取模块及其所有子模块的ID列表
const getAllModuleIdsFromTree = (moduleId, moduleNode) => {
  const moduleIds = [moduleId]

  // 递归收集子模块ID
  const collectChildIds = (node) => {
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        if (child.type === 'module') {
          moduleIds.push(child.moduleId)
          collectChildIds(child)
        }
      })
    }
  }

  // 从当前节点开始收集子模块
  collectChildIds(moduleNode)

  return moduleIds
}

// 高亮搜索关键词
const highlightSearchKeyword = (text) => {
  if (!searchKeyword.value || !searchKeyword.value.trim()) {
    return text
  }

  const keyword = searchKeyword.value.trim()
  // 转义特殊字符，防止正则表达式错误
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  return text.replace(regex, '<span style="background-color: #ffeb3b; color: #000; font-weight: bold; padding: 1px 2px; border-radius: 2px;">$1</span>')
}

// 复制用例
const handleCopy = (row) => {
  copyForm.value = {
    id: row.id,
    case_name: `${row.case_name}_副本`,
    project_id: selectedProjectId.value || row.project_id
  }
  copyModalVisible.value = true
}

// 执行复制
const executeCopy = async () => {
  try {
    await testCaseApi.copyTestCase(copyForm.value)
    $message.success('用例复制成功')
    copyModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('复制失败: ' + error.message)
  }
}

// 重置编号
const handleResetNumbers = async () => {
  try {
    await testCaseApi.resetTestCaseNumbers(selectedProjectId.value)
    $message.success('编号重置成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('重置失败: ' + error.message)
  }
}

// 新增函数
const handleAdd = async () => {
  originalHandleAdd()

  if (selectedProjectId.value) {
    modalForm.value.project_id = String(selectedProjectId.value)
    await loadModuleOptions(selectedProjectId.value)
  }
}

// 编辑函数
const handleEdit = async (row) => {
  const editRow = {...row}
  if (editRow.project_id !== undefined && editRow.project_id !== null) {
    editRow.project_id = String(editRow.project_id)
    await loadModuleOptions(editRow.project_id)
  }
  originalHandleEdit(editRow)
}

// 保存函数
const handleSave = async () => {
  try {
    await originalHandleSave()

    // 保存成功后刷新表格
    const projectId = selectedProjectId.value || modalForm.value.project_id
    if (projectId) {
      queryItems.value.project_id = projectId
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('保存失败:', error)
    $table.value?.handleSearch()
  }
}

// 处理项目变化
const handleProjectChange = async (projectId) => {
  if (projectId) {
    await loadModuleOptions(projectId)
    modalForm.value.module_id = null
  }
}

// 自定义重置处理 - 保持当前项目ID
const handleReset = async () => {
  console.log('=== 功能测试用例重置函数开始 ===')
  console.log('重置前 selectedProjectId:', selectedProjectId.value)
  console.log('重置前 queryItems:', queryItems.value)

  // 确保有选中的项目ID
  if (!selectedProjectId.value) {
    console.warn('没有选中的项目ID，使用默认重置逻辑')
    // 如果没有选中项目，清空所有查询条件
    queryItems.value = {
      case_name: null,
      case_level: null,
      module_id: null,
      module_ids: null,
      status: null,
      source: null,
      is_smoke: null,
      project_id: null
    }
  } else {
    // 重置查询条件，但保持当前选中的项目ID
    queryItems.value = {
      case_name: null,
      case_level: null,
      module_id: null,
      module_ids: null,
      status: null,
      source: null,
      is_smoke: null,
      project_id: selectedProjectId.value
    }
  }

  console.log('重置后 queryItems:', queryItems.value)

  // 等待响应式更新完成
  await nextTick()

  // 刷新表格数据
  $table.value?.handleSearch()
  console.log('=== 功能测试用例重置函数结束 ===')
}

// 监听 queryItems 变化，确保 project_id 不会被意外清空
watch(queryItems, (newVal, oldVal) => {
  console.log('功能测试用例 queryItems 变化:', {oldVal, newVal})

  // 如果 project_id 被清空但我们有选中的项目，则恢复它
  if (!newVal.project_id && selectedProjectId.value) {
    console.log('检测到 project_id 被清空，恢复为:', selectedProjectId.value)
    newVal.project_id = selectedProjectId.value
  }
}, {deep: true})

// 更新状态
const handleUpdateStatus = async (testCaseId, newStatus) => {
  try {
    // 获取当前测试用例的完整信息
    const currentData = $table.value?.tableData?.find(item => item.id === testCaseId)
    if (!currentData) {
      $message.error('未找到测试用例数据')
      return
    }

    // 构建更新数据
    const updateData = {
      id: testCaseId,
      case_name: currentData.case_name,
      case_level: currentData.case_level,
      precondition: currentData.precondition,
      test_steps: currentData.test_steps,
      expected_result: currentData.expected_result,
      is_smoke: currentData.is_smoke,
      status: newStatus,
      source: currentData.source,
      project_id: currentData.project_id,
      module_id: currentData.module_id
    }

    await testCaseApi.updateTestCase(updateData)
    $message.success('状态更新成功')

    // 刷新表格
    $table.value?.handleSearch()
  } catch (error) {
    console.error('更新状态失败:', error)
    $message.error('状态更新失败: ' + (error.message || '未知错误'))
  }
}

// 导入相关函数
const handleImport = () => {
  importModalVisible.value = true
  importResult.value = null
  fileList.value = []
  selectedModuleId.value = null
  // 加载模块选项
  if (selectedProjectId.value) {
    loadModuleOptions(selectedProjectId.value)
  }
}

const handleDownloadTemplate = async () => {
  try {
    downloadLoading.value = true
    const response = await testCaseApi.downloadTemplate()

    // 检查响应是否为Blob
    let blob
    if (response instanceof Blob) {
      blob = response
    } else if (response.data instanceof Blob) {
      blob = response.data
    } else {
      // 如果响应不是Blob，创建一个Blob
      blob = new Blob([response.data || response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '功能测试用例导入模板.xlsx'
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    $message.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    $message.error('下载模板失败: ' + (error.response?.data?.msg || error.message))
  } finally {
    downloadLoading.value = false
  }
}

const handleFileChange = (options) => {
  fileList.value = options.fileList
  importResult.value = null
}

const handleFileRemove = () => {
  fileList.value = []
  importResult.value = null
}

const handleExecuteImport = async () => {
  if (fileList.value.length === 0) {
    $message.error('请选择要导入的文件')
    return
  }

  if (!selectedProjectId.value) {
    $message.error('请先选择项目')
    return
  }

  try {
    importLoading.value = true
    importResult.value = null

    const params = {
      file: fileList.value[0].file,
      project_id: selectedProjectId.value
    }

    if (selectedModuleId.value) {
      params.module_id = selectedModuleId.value
    }

    const response = await testCaseApi.importTestCases(params)

    if (response.code === 200) {
      importResult.value = {
        success: true,
        message: response.msg,
        data: response.data
      }

      // 如果有成功导入的数据，刷新表格
      if (response.data && response.data.success_count > 0) {
        $table.value?.handleSearch()
      }
    } else {
      importResult.value = {
        success: false,
        message: response.msg || '导入失败',
        data: response.data
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    importResult.value = {
      success: false,
      message: '导入失败: ' + error.message,
      data: null
    }
  } finally {
    importLoading.value = false
  }
}

const handleCloseImportModal = () => {
  importModalVisible.value = false
  importResult.value = null
  fileList.value = []
  selectedModuleId.value = null
}

// 批量操作相关方法
const handleTableChecked = (rowKeys) => {
  selectedRowKeys.value = rowKeys
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    $message.warning('请先选择要删除的测试用例')
    return
  }

  $dialog.warning({
    title: '确认删除',
    content: `确定要删除选中测试用例吗？删除后将自动重置编号，此操作不可恢复。`,
    // content: `确定要删除选中的 ${selectedRowKeys.value.length} 个测试用例吗？删除后将自动重置编号，此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await testCaseApi.batchDeleteTestCases({ids: selectedRowKeys.value})
        // $message.success(`成功删除 ${selectedRowKeys.value.length} 个测试用例`)
        $message.success(`成功删除测试用例`)
        selectedRowKeys.value = []
        $table.value?.handleSearch()
      } catch (error) {
        $message.error('批量删除失败: ' + error.message)
      }
    }
  })
}

const handleBatchUpdateStatus = () => {
  if (selectedRowKeys.value.length === 0) {
    $message.warning('请先选择要修改状态的测试用例')
    return
  }
  batchStatusModalVisible.value = true
}

const executeBatchUpdateStatus = async () => {
  try {
    await testCaseApi.batchUpdateTestCaseStatus({
      ids: selectedRowKeys.value,
      status: batchStatus.value
    })
    $message.success(`成功修改测试用例的状态`)
    batchStatusModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('批量修改状态失败: ' + error.message)
  }
}

// 生命周期
onMounted(() => {
  loadProjectList()
})
</script>

<style scoped>
.n-layout-page-header {
  display: flex;
  flex-direction: column;
  min-height: 0;
  flex: 1;
}

/* 搜索高亮样式 */
:deep(.n-tree-node-content) {
  overflow: visible !important;
}

:deep(.n-tree-node-content__text) {
  overflow: visible !important;
  white-space: nowrap;
}

/* 树节点悬停效果 */
:deep(.n-tree-node:hover .n-tree-node-content) {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 选中节点样式 */
:deep(.n-tree-node--selected .n-tree-node-content) {
  background-color: rgba(24, 144, 255, 0.2);
  font-weight: 500;
}
</style>
