import { request } from '@/utils'

const BASE_URL = '/ai_test_case_generation'

export default {
  // 生成功能测试用例 - 设置30秒超时
  generateTestCases: (data) => request.post(`${BASE_URL}/generate`, data, { timeout: 30000 }),

  // 保存生成的测试用例
  saveGeneratedTestCases: (data) => request.post(`${BASE_URL}/save`, data),

  // 获取生成历史 - 转换数据格式以适配CrudTable组件
  getGenerationHistory: async (params) => {
    const response = await request.get(`${BASE_URL}/history`, { params })
    // 转换数据格式：从 {code, msg, data, total, page, page_size} 转换为 {data, total}
    return {
      data: response.data || [],
      total: response.total || 0
    }
  },

  // 获取生成任务详情
  getGenerationTask: (taskId) => request.get(`${BASE_URL}/task/${taskId}`),

  // 获取任务生成结果
  getTaskGenerationResult: (taskId) => request.get(`${BASE_URL}/task/${taskId}/result`),

  // 删除生成任务
  deleteGenerationTask: (taskId) => request.delete(`${BASE_URL}/task/${taskId}`)
}
